import { CancellationToken, CancellationTokenSource } from '../../../../base/common/cancellation.js';
import { URI } from '../../../../base/common/uri.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator, IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { QueryBuilder } from '../../../services/search/common/queryBuilder.js';
import { ISearchService } from '../../../services/search/common/search.js';
import { ICodeseekLogger } from './codeseekLogService.js';
import { ICtagsSymbolService } from '../browser/ctagsSymbolService.js';
import { SymbolDefinition } from '../electron-main/ctags/ctagsRunner.js';
import { IClangdSymbolService } from '../browser/clangdSymbolService.js';
import { filenameToVscodeLanguage } from './helpers/detectLanguage.js';
import { convertFilePathToUri, getRelativePath, getWorkspaceUri } from './helpers/path.js';
import {
	ApproveRequestResultType, AskReponseType, AskResponse, codeseekTools,
	DirectoryItem, ToolCallParamsType, ToolCallReturnType, ToolFns, ToolName,
	ToolNameEnum, ToolResultToString, ToolCallStatus, ToolCallResultType, ToolCallType,
	AdditionalOpts
} from './toolsServiceTypes.js';
import { LogLevel } from '../../../../platform/log/common/log.js';
import { AskMessage, MessageFrom, PluginMessageOpts, userMessageOpts } from '../browser/chatThreadType.js';
import { IChatThreadService } from '../browser/chatThreadService.js';
import { IPluginTaskService } from '../browser/pluginTaskService.js';
import pWaitFor from '../../../common/pWaitFor.js';
import { CodebaseSelection } from './selectedFileServiceType.js';
import { IEditCodeService } from '../browser/editCodeService.js';
import { ITerminalToolService } from '../browser/terminalToolService.js';
import { IRipGrepTool } from './tools/ripGrepTool.js';
import { ChatMode } from './codeseekSettingsService.js';
import { ICallTraceQueryTool } from './tools/callTraceQueryTool.js';
import { ICodebaseSearchTool } from './tools/codebaseSearchTool.js';
import { CodebaseSearchToolResult } from './toolsServiceTypes.js';
import { IReadFileTool } from './tools/readFileTool.js';
import { tripleTick } from './prompt/tags.js';
import * as path from '../../../../base/common/path.js';


const MAX_CHILDREN_URIs_PAGE = 500;
const computeDirectoryResult = async (
	fileService: IFileService,
	rootURI: URI,
	pageNumber: number = 1
): Promise<ToolCallReturnType[ToolNameEnum.LIST_DIR]> => {
	const stat = await fileService.resolve(rootURI, { resolveMetadata: false });
	if (!stat.isDirectory) {
		return { rootURI, children: null, hasPrevPage: false, itemsRemaining: 0 };
	}

	const originalChildrenLength = stat.children?.length ?? 0;
	const fromChildIdx = MAX_CHILDREN_URIs_PAGE * (pageNumber - 1);
	const toChildIdx = MAX_CHILDREN_URIs_PAGE * pageNumber - 1; // INCLUSIVE
	const listChildren = stat.children?.slice(fromChildIdx, toChildIdx + 1) ?? [];

	const children: DirectoryItem[] = listChildren.map(child => ({
		uri: child.resource,
		name: child.name,
		isDirectory: child.isDirectory,
		isSymbolicLink: child.isSymbolicLink || false
	}));

	const hasPrevPage = pageNumber > 1;
	const itemsRemaining = Math.max(0, originalChildrenLength - (toChildIdx + 1));

	return {
		rootURI,
		children,
		hasPrevPage,
		itemsRemaining
	};
};


export const fileContentsToString = (result: ToolCallReturnType[ToolNameEnum.READ_FILE], workspaceService: IWorkspaceContextService): string => {
	const relativePath = getRelativePath(workspaceService, result.uri.fsPath);
	const totalStr = result.totalLines === result.endLine ? `(entire file)` : `(total ${result.totalLines} lines)`;
	const lines = result.fileContent.split('\n');
	const numberedLines = lines.map((line, index) => `${result.startLine + index}|${line}`).join('\n');

	return `Contents of ${relativePath}, lines ${result.startLine}-${result.endLine} ${totalStr}:\n${numberedLines}`;
};

export const codebaseContentToString = (codebaseSelections: CodebaseSelection[], workspaceUri: URI): string => {
	if (codebaseSelections.length === 0) {
		return '';
	}
	const fullResult = codebaseSelections.slice(0, 3).map(sel => {
		const relativePath = path.relative(workspaceUri.fsPath, sel.fileURI.fsPath);
		const startLine = sel.range.startLineNumber;
		const endLine = sel.range.endLineNumber;
		const content = sel.selectionStr.split('\n').map((line, index) => `${startLine + index}|${line}`).join('\n');
		return `\
<search_result path="${relativePath}" startLine="${startLine}" endLine="${endLine}">
${content}
</search_result>
`;
	});

	const remainingResult = codebaseSelections.slice(3).map(sel => {
		const relativePath = path.relative(workspaceUri.fsPath, sel.fileURI.fsPath);
		const startLine = sel.range.startLineNumber;
		const endLine = sel.range.endLineNumber;
		let content = '';
		if (endLine - startLine <= 5) {
			content = '\n' + sel.selectionStr.split('\n').map((line, index) => `${startLine + index}|${line}`).join('\n') + '\n';
		}
		return `\
<search_result path="${relativePath}" startLine="${startLine}" endLine="${endLine}">${content}</search_result>
`;
	});

	return `\
These may or may not include the full answer. It is up to you to decide if you need to call more tools to gather more information.

The top search results are shown in full:
${fullResult.join('\n')}

Below are the signatures for the remaining search results. If you believe that exploring these chunks or files may reveal additional relevant information that is useful for completing your task, consider using the grep_search tool or read_file tool.

${remainingResult.join('\n')}
`.trim();
};

const directoryResultToString = (result: ToolCallReturnType[ToolNameEnum.LIST_DIR]): string => {
	if (!result.children) {
		return `Error: ${result.rootURI} is not a directory`;
	}

	let output = '';
	const entries = result.children;

	if (!result.hasPrevPage) {
		output += `${result.rootURI}\n`;
	}

	for (let i = 0; i < entries.length; i++) {
		const entry = entries[i];
		const isLast = i === entries.length - 1;
		const prefix = isLast ? '└── ' : '├── ';

		output += `${prefix}${entry.name}${entry.isDirectory ? '/' : ''}${entry.isSymbolicLink ? ' (symbolic link)' : ''}\n`;
	}

	return output;
};

const validateQueryStr = (queryStr: unknown) => {
	if (typeof queryStr !== 'string') throw new Error('provided query must be a string.');
	return queryStr;
};


// TODO!!!! check to make sure in workspace
const validateURI = (uriStr: unknown, _workspaceContextService: IWorkspaceContextService) => {
	if (typeof uriStr !== 'string') throw new Error('provided uri must be a string.');
	return convertFilePathToUri(uriStr, _workspaceContextService);
};

export interface IToolsService {
	readonly _serviceBrand: undefined;
	toolFns: ToolFns;
	toolResultToString: ToolResultToString;
	isNeedApprove(toolName: ToolName): boolean;

	findSymbolCtagsDefinitions(symbolName: string, className?: string): Promise<SymbolDefinition[]>;
	executeTool(containerId: string, threadId: string, chatId: string, toolCall: ToolCallType, userMessageOpts: userMessageOpts): Promise<ToolCallResultType>;
	cancelExecTool(threadId: string): void;

	isSameToolCall(toolCall1: ToolCallType, toolCall2: ToolCallType): boolean;
}

export const IToolsService = createDecorator<IToolsService>('ToolsService');


function parseCommandWithWorkdir(commandStr: string): { workdir: string | undefined; command: string } {
	const cdPattern = /^\s*cd\s+([^\s&]+)\s+&&\s+(.+)$/;
	const match = commandStr.match(cdPattern);

	if (match) {
		return {
			workdir: match[1],
			command: match[2].trim()
		};
	}

	return {
		workdir: undefined,
		command: commandStr.trim()
	};
}

export class ToolsService implements IToolsService {

	readonly _serviceBrand: undefined;

	public toolFns: ToolFns;
	public toolResultToString: ToolResultToString;
	private readonly threadId2CancelTokens: Map<string, CancellationTokenSource> = new Map();

	constructor(
		@IFileService fileService: IFileService,
		@IWorkspaceContextService private readonly _workspaceContextService: IWorkspaceContextService,
		@ISearchService searchService: ISearchService,
		@IInstantiationService private readonly instantiationService: IInstantiationService,
		@ICtagsSymbolService private readonly _ctagsSymbolService: ICtagsSymbolService,
		@ICodeseekLogger private readonly _codeseekLogService: ICodeseekLogger,
		@IClangdSymbolService private readonly _clangdSymbolService: IClangdSymbolService,
		@IInstantiationService private readonly _instantiationService: IInstantiationService,
		@IPluginTaskService private readonly _pluginTaskService: IPluginTaskService,
		@ITerminalToolService private readonly flowTerminalService: ITerminalToolService,
	) {
		_codeseekLogService.setLevel(LogLevel.Info);
		const queryBuilder = instantiationService.createInstance(QueryBuilder);
		this.toolFns = {
			[ToolNameEnum.READ_FILE]: async (p: ToolCallParamsType[ToolNameEnum.READ_FILE], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const { path: uriStr, start_line_one_indexed, end_line_one_indexed, should_read_entire_file } = p;

				const uri = validateURI(uriStr, this._workspaceContextService);
				if (uri.path.endsWith('/')) {
					throw new Error(`Read file failed, ${uriStr} is a folder`);
				}
				const isExist = await fileService.exists(uri);
				if (!isExist) {
					throw new Error(`Could not find file '${uri.fsPath}' in workspace`);
				}
				this._codeseekLogService.info('read file', uri.fsPath);
				const readFileTool = this.instantiationService.invokeFunction(accessor => accessor.get(IReadFileTool));
				const { status, message, result } = await readFileTool.execute({
					uri,
					shouldReadEntireFile: should_read_entire_file,
					startLineOneIndexed: start_line_one_indexed,
					endLineOneIndexed: end_line_one_indexed
				});
				if (status === 'success') {
					return { uri, fileContent: result.content, startLine: result.startLine, endLine: result.endLine, totalLines: result.totalLines };
				} else if (status === 'timeout') {
					throw new Error(message);
				} else {
					throw new Error(message);
				}
			},
			[ToolNameEnum.EDIT_FILE]: async (p: ToolCallParamsType[ToolNameEnum.EDIT_FILE], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const { path, code_block, executeApply } = p;

				const uri = validateURI(path, this._workspaceContextService);

				if (executeApply) {
					const uuid = crypto.randomUUID();
					const opts = {
						from: 'ClickApply',
						type: 'rewrite',
						applyStr: code_block,
						chatId: additionalOpt.chatId,
						sessionId: additionalOpt.sessionId,
						businessEvent: additionalOpt.businessEvent,
						uri,
						applyBoxId: uuid,
					} as const;
					// 延迟获取 IEditCodeService 以避免循环依赖
					const editCodeService = this.instantiationService.invokeFunction(accessor => accessor.get(IEditCodeService));

					// 获取 ChatThreadService 并设置Apply状态
					const chatThreadService = this.instantiationService.invokeFunction(accessor => accessor.get(IChatThreadService));
					chatThreadService.setApplyingState(uri.toString(), true);

					try {

						await editCodeService.startApplying(opts);
						return { content: `success to edit file ${path}` };
					} catch (error) {
						// 在出错时清理Apply状态
						chatThreadService.setApplyingState(uri.toString(), false);
						throw error;
					}
				} else {
					return { content: `success to generate code for file ${path}` };
				}

			},
			[ToolNameEnum.LIST_DIR]: async (p: ToolCallParamsType[ToolNameEnum.LIST_DIR], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const { relative_workspace_path: pathStr } = p;

				const uri = validateURI(pathStr, this._workspaceContextService);

				const dirResult = await computeDirectoryResult(fileService, uri);

				return dirResult;
			},
			[ToolNameEnum.FILE_SEARCH]: async (p: ToolCallParamsType[ToolNameEnum.FILE_SEARCH], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const { query: queryUnknown } = p;

				const queryStr = validateQueryStr(queryUnknown);
				const { workspaceUri } = getWorkspaceUri(this._workspaceContextService);
				if (!workspaceUri) {
					throw new Error('Could not find workspace');
				}
				const query = queryBuilder.file([workspaceUri], { filePattern: queryStr, });
				const data = await searchService.fileSearch(query, CancellationToken.None);

				const uris = data.results
					.map(({ resource, results }) => resource);
				if (typeof uris === 'string') {
					return { queryStr, uris: [uris] };
				}
				return { queryStr, uris };
			},
			[ToolNameEnum.DELETE_FILE]: async (p: ToolCallParamsType[ToolNameEnum.DELETE_FILE], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const { target_file: targetFileStr } = p;

				const uri = validateURI(targetFileStr, this._workspaceContextService);
				const isExist = await fileService.exists(uri);
				if (!isExist) {
					return { content: `file ${targetFileStr} does not exist` };
				}

				try {
					await fileService.del(uri);
					return { content: `成功删除文件 ${targetFileStr}` };
				} catch (error) {
					this._codeseekLogService.error('删除文件失败:', error);
					throw new Error(`Failed to delete file ${targetFileStr}`);
				}
			},
			[ToolNameEnum.CREATE_FILE]: async (p: ToolCallParamsType[ToolNameEnum.CREATE_FILE], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const { path: pathStr } = p;

				const uri = validateURI(pathStr, this._workspaceContextService);

				return { uri };
			},
			[ToolNameEnum.APPROVE_REQUEST]: async (p: ToolCallParamsType[ToolNameEnum.APPROVE_REQUEST], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const response: AskResponse = await callback?.();
				if (response.response === AskReponseType.yesButtonClicked) {
					return { content: '用户同意此操作', response: AskReponseType.yesButtonClicked };
				}
				else if (response.response === AskReponseType.noButtonClicked) {
					return { content: '用户拒绝此操作', response: AskReponseType.noButtonClicked };
				}
				else {
					throw new Error('Invalid response type');
				}
			},
			[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: async (p: ToolCallParamsType[ToolNameEnum.ASK_FOLLOWUP_QUESTION], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const response: AskResponse = await callback?.();
				if (response.response === AskReponseType.messageResponse) {
					return { content: response.text || '', selectedIndex: response.selectedIndex ?? 0 };
				}
				else {
					throw new Error('Invalid response type');
				}
			},
			[ToolNameEnum.CTAGS_QUERY]: async (p: ToolCallParamsType[ToolNameEnum.CTAGS_QUERY], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const { symbol: symbolStr, className } = p;

				if (typeof symbolStr !== 'string') throw new Error('provided symbol must be a string.');
				const symbol = symbolStr;
				const definitions = await this.findSymbolCtagsDefinitions(symbol, className);
				_codeseekLogService.info('ctags definitions result:', JSON.stringify(definitions));
				return definitions.map(def => ({
					rawLineContent: def.rawLineContent,
					name: def.name,
					path: def.path,
					scopePath: def.scopePath,
					line: def.line,
					kind: def.kind,
					language: def.language,
					positions: def.positions,
					scope: def.scope
				}));
			},
			[ToolNameEnum.CLANGD_QUERY]: async (p: ToolCallParamsType[ToolNameEnum.CLANGD_QUERY], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const { filePath, line, character } = p;

				if (typeof filePath !== 'string') throw new Error('provided filePath must be a string.');
				if (typeof line !== 'number') throw new Error('provided line must be a number.');
				if (typeof character !== 'number') throw new Error('provided character must be a number.');

				const uri = convertFilePathToUri(filePath, this._workspaceContextService);
				const result = await this._clangdSymbolService.getSymbolReferences(uri, line, character);
				return result;
			},
			[ToolNameEnum.SHOW_SUMMARY]: async (p: ToolCallParamsType[ToolNameEnum.SHOW_SUMMARY], additionalOpt: AdditionalOpts, callback?: () => any) => {

			},
			[ToolNameEnum.EXEC_COMMAND]: async (p: ToolCallParamsType[ToolNameEnum.EXEC_COMMAND], additionalOpt: AdditionalOpts, callback?: () => any) => {
				let workDir: string | undefined = undefined;
				if (p.workdir !== undefined) {
					const uri = validateURI(p.workdir, this._workspaceContextService);
					workDir = uri.fsPath;
				}
				if (!additionalOpt) {
					this._codeseekLogService.error('theadditionalOpt parameter is required');
					throw new Error('Tool internal error');
				}
				const chatThreadService = this._instantiationService.invokeFunction(accessor => accessor.get(IChatThreadService));
				const onWait = async () => {
					chatThreadService.setStreamState(additionalOpt.containerId, additionalOpt.threadId, { isLoading: false });
					const askMessage: AskMessage = {
						type: 'tool',
						content: {
							name: ToolNameEnum.EXEC_COMMAND,
							params: p,
							id: additionalOpt.toolCallId,
						},
					};

					chatThreadService.setNoStorageState(additionalOpt.threadId, { askMessage })
					await pWaitFor(() => chatThreadService.getNoStorageState(additionalOpt.threadId)?.askResponse !== undefined, { interval: 100 });
					const curThreadNoStorageState = chatThreadService.getNoStorageState(additionalOpt.threadId);
					const askResponse = curThreadNoStorageState!.askResponse
					const response = { type: askResponse!.type, response: askResponse?.response, text: askResponse?.text };
					chatThreadService.setNoStorageState(additionalOpt.threadId, { askMessage: undefined, askResponse: undefined, askResponseText: undefined });
					chatThreadService.setStreamState(additionalOpt.containerId, additionalOpt.threadId, { isLoading: true });
					return response;
				};

				const response: AskResponse = await onWait();
				let command = p.command;
				if (response.text) {
					const parsedCommand = parseCommandWithWorkdir(response.text);
					if (parsedCommand.workdir) {
						workDir = validateURI(parsedCommand.workdir, this._workspaceContextService).fsPath;
					}
					command = parsedCommand.command;
					chatThreadService.updateToolCall(additionalOpt.containerId, additionalOpt.threadId, {
						toolCall: {
							...p,
							params: {
								command: parsedCommand.command,
								workdir: workDir,
							} as ToolCallParamsType[ToolNameEnum.EXEC_COMMAND],
						} as unknown as ToolCallType,
					});
				}
				if (response.response === AskReponseType.yesButtonClicked) {
					chatThreadService.updateToolCall(additionalOpt.containerId, additionalOpt.threadId, {
						toolCallResult: {
							status: ToolCallStatus.executing,
						} as ToolCallResultType,
					});
					try {
						let output;
						output = await this.flowTerminalService.runCommandOnNewInstance(command, workDir, additionalOpt.cancelToken)
						return {
							command: `cd ${workDir} && ${command}`,
							status: ToolCallStatus.success,
							output
						};
					} catch (error) {
						throw new Error("命令执行失败");
					}
				} else {
					this._codeseekLogService.info('拒绝执行命令:', command);
					return {
						command: `cd ${workDir} && ${command}`,
						status: ToolCallStatus.rejected,
						output: `拒绝执行${command}命令`
					};
				}
			},
			[ToolNameEnum.SHOW_CONTENT]: async (p: ToolCallParamsType[ToolNameEnum.SHOW_CONTENT], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const content = "success to show content"
				return { content };
			},
			[ToolNameEnum.CODEBASE_SEARCH]: async (p: ToolCallParamsType[ToolNameEnum.CODEBASE_SEARCH], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const { query } = p;
				const workspaceUri = getWorkspaceUri(this._workspaceContextService).workspaceUri;
				if (!workspaceUri) {
					throw new Error('Could not find workspace');
				}
				const codebaseSearchTool = this.instantiationService.invokeFunction(accessor => accessor.get(ICodebaseSearchTool));
				const codebaseSearchResult = await codebaseSearchTool.execute({ query, workspaceUri });
				if (codebaseSearchResult.status === 'success') {
					return { searchResults: codebaseSearchResult.result };
				} else if (codebaseSearchResult.status === 'timeout') {
					throw new Error(codebaseSearchResult.message);
				} else {
					return { searchResults: codebaseSearchResult.result };
				}
			},
			[ToolNameEnum.GREP_SEARCH]: async (p: ToolCallParamsType[ToolNameEnum.GREP_SEARCH], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const { query, case_sensitive, exclude_pattern, include_pattern, explanation } = p;

				const grepTool = this.instantiationService.invokeFunction(accessor => accessor.get(IRipGrepTool));
				const ripGrepResult = await grepTool.execute({ query, case_sensitive, exclude_pattern, include_pattern, explanation });
				if (ripGrepResult.status === 'success') {
					return {
						result: ripGrepResult.result.map(result => ({
							...result,
							uri: URI.file(result.filePath),
						}))
					};
				} else if (ripGrepResult.status === 'timeout') {
					throw new Error(ripGrepResult.message);
				} else {
					return {
						result: ripGrepResult.result.map(result => ({
							...result,
							uri: URI.file(result.filePath),
						}))
					};
				}
			},
			[ToolNameEnum.CALL_TRACE_QUERY]: async (p: ToolCallParamsType[ToolNameEnum.CALL_TRACE_QUERY], additionalOpt: AdditionalOpts, callback?: () => any) => {
				const { relative_path, function: functionName } = p;
				const callTraceQueryTool = this.instantiationService.invokeFunction(accessor => accessor.get(ICallTraceQueryTool));
				const callTraceQueryResult = await callTraceQueryTool.execute({ relativePath: relative_path, function: functionName });
				if (callTraceQueryResult.status === 'success') {
					return { content: callTraceQueryResult.result };
				} else if (callTraceQueryResult.status === 'timeout') {
					throw new Error(callTraceQueryResult.message);
				} else {
					return { content: callTraceQueryResult.message };
				}
			}
		};


		this.toolResultToString = {
			[ToolNameEnum.READ_FILE]: (result) => {
				const fileContents = fileContentsToString(result, this._workspaceContextService);
				return fileContents;
			},
			[ToolNameEnum.EDIT_FILE]: (result) => {
				return result.content;
			},
			[ToolNameEnum.LIST_DIR]: (result) => {
				if (!result || !result.children || result.children.length === 0) {
					return 'Error: no result from tool.';
				}
				const dirTreeStr = directoryResultToString(result);
				return dirTreeStr;
			},
			[ToolNameEnum.FILE_SEARCH]: (result) => {
				if (!result || !result.uris || result.uris.length === 0) {
					return 'Error: no result from tool.';
				}
				return result.uris.map(uri => uri.fsPath).join('\n');
			},
			[ToolNameEnum.DELETE_FILE]: (result) => {
				return result.content;
			},
			[ToolNameEnum.CREATE_FILE]: (result) => {
				return "File created successfully";
			},
			[ToolNameEnum.APPROVE_REQUEST]: (result) => {
				return result.content;
			},
			[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: (result) => {
				return result.content;
			},
			[ToolNameEnum.CTAGS_QUERY]: (result) => {
				if (!result || result.length === 0) {
					return 'Error: no result from tool.';
				}
				if (!Array.isArray(result)) return 'Error: Invalid result format';
				return JSON.stringify(result);
			},
			[ToolNameEnum.CLANGD_QUERY]: (result) => {
				if (!result || result.length === 0) {
					return 'Error: no result from tool.';
				}
				if (!Array.isArray(result)) return 'Error: Invalid result format';
				return JSON.stringify(result);
			},
			[ToolNameEnum.SHOW_SUMMARY]: () => {
				return 'success to show'
			},
			[ToolNameEnum.SHOW_CONTENT]: (result) => {
				return result.content
			},
			[ToolNameEnum.CODEBASE_SEARCH]: (result: CodebaseSearchToolResult) => {
				if (result.searchResults.length === 0) {
					return 'Error: no result from tool.';
				}
				const codebaseSelections: CodebaseSelection[] = result.searchResults.map(r => ({
					type: 'Codebase' as const,
					fileURI: r.uri,
					title: 'Codebase',
					selectionStr: r.content,
					range: r.range,
					fromMention: false
				}));
				const workspaceUri = getWorkspaceUri(this._workspaceContextService).workspaceUri;
				if (!workspaceUri) {
					return 'Error: Could not find workspace';
				}
				const codebaseStr = codebaseContentToString(codebaseSelections, workspaceUri);
				return codebaseStr || '';
			},
			[ToolNameEnum.EXEC_COMMAND]: (result) => {
				if (result.status === ToolCallStatus.rejected) {
					return 'The user chose not to run this terminal command. ASK THE USER what they would like to do next.'
				} else {
					return `实际执行命令为:  ${result.command}\n 执行结果为: ${result.output}`;
				}
			},
			[ToolNameEnum.GREP_SEARCH]: (result) => {
				if (!result.result || result.result.length === 0) {
					return 'Error: no result from tool.';
				}
				return result.result.map(r => {
					const language = filenameToVscodeLanguage(r.uri.path) ?? '';
					const languagePrefix = language ? `${language}:` : '';
					const lineRange = `(lines ${r.range.startLineNumber}-${r.range.endLineNumber})`;
					return `${tripleTick[0]}${languagePrefix}${r.uri.fsPath} ${lineRange}\n${r.content}\n${tripleTick[1]}`
				}).join('\n');
			},
			[ToolNameEnum.CALL_TRACE_QUERY]: (result) => {
				if (!result || !result.content) {
					return 'Error: no result from tool.';
				}
				return result.content;
			}
		};
	}

	isNeedApprove(toolName: ToolName) {
		return codeseekTools[toolName].needApprove;
	}

	/**
	 * 使用 ctags 查找符号定义
	 * @param symbolName 要查找的符号名称
	 * @param className 类名过滤（可选）
	 * @param filter 可选的过滤条件
	 * @returns 找到的符号定义数组
	 */
	async findSymbolCtagsDefinitions(symbolName: string, className?: string): Promise<SymbolDefinition[]> {
		try {
			// 获取当前工作区的所有根目录
			const workspaceFolders = this._workspaceContextService.getWorkspace().folders;
			if (!workspaceFolders.length) {
				return [];
			}

			// 将所有工作区文件夹作为搜索范围
			const scopeDirs = workspaceFolders.map(folder => folder.uri);

			// 调用 ctags 服务进行符号查找
			const results = await this._ctagsSymbolService.getSymbolDefinitions(symbolName, scopeDirs, { class: className });
			return results;
		} catch (error) {
			this._codeseekLogService.error('查找符号定义失败:', error);
			return [];
		}
	}

	isSameToolCall(toolCall1: ToolCallType, toolCall2: ToolCallType): boolean {
		return toolCall1.name === toolCall2.name && JSON.stringify(toolCall1.params) === JSON.stringify(toolCall2.params);
	}

	async executeTool(containerId: string, threadId: string, chatId: string, toolCall: ToolCallType, userMessageOpts: userMessageOpts): Promise<ToolCallResultType> {
		const toolName = toolCall.name as ToolName;
		let toolResultVal: ToolCallReturnType[ToolName];
		let content: string = '';
		const ideTool = this.toolFns[toolName];
		const chatThreadService = this._instantiationService.invokeFunction(accessor => accessor.get(IChatThreadService));
		const cancellationSource = new CancellationTokenSource();
		this.threadId2CancelTokens.set(threadId, cancellationSource);
		const additionalOpt: AdditionalOpts = {
			toolCallId: toolCall.id,
			cancelToken: cancellationSource.token,
			containerId,
			threadId,
			businessEvent: userMessageOpts.businessEvent,
			sessionId: threadId,
			chatId,
		}
		const onWait = async () => {
			chatThreadService.setStreamState(containerId, threadId, { isLoading: false });
			const askMessage: AskMessage = {
				type: 'tool',
				content: toolCall,
			};

			chatThreadService.setNoStorageState(threadId, { askMessage })
			await pWaitFor(() => chatThreadService.getNoStorageState(threadId)?.askResponse !== undefined, { interval: 100 });
			const curThreadNoStorageState = chatThreadService.getNoStorageState(threadId);
			const askResponse = curThreadNoStorageState!.askResponse
			const response = { type: askResponse!.type, response: askResponse?.response, text: askResponse?.text, selectedIndex: askResponse?.selectedIndex };
			chatThreadService.setNoStorageState(threadId, { askMessage: undefined, askResponse: undefined, askResponseText: undefined });
			chatThreadService.setStreamState(containerId, threadId, { isLoading: true });
			return response;
		};
		if (ideTool === undefined) {
			let externalToolsNeedApprove = true;
			const filteredTools = (userMessageOpts as PluginMessageOpts).taskInfo?.externalTools?.filter(tool => tool.name === toolName);
			if (filteredTools && filteredTools.length > 0) {
				externalToolsNeedApprove = filteredTools[0].needApprove;
			}
			if (externalToolsNeedApprove) {
				toolResultVal = await this.toolFns[ToolNameEnum.APPROVE_REQUEST](toolCall.params as any, additionalOpt, onWait);
				const approveResult = toolResultVal as ApproveRequestResultType;
				if (approveResult.response === AskReponseType.yesButtonClicked && userMessageOpts.from === MessageFrom.Plugin) {
					this._pluginTaskService.fireToolCall((userMessageOpts as PluginMessageOpts).taskInfo.taskId, toolName, toolCall.params);
				}
				content = this.toolResultToString[ToolNameEnum.APPROVE_REQUEST](toolResultVal as any);
				return { status: ToolCallStatus.success, name: toolName, error: '', content, toolCallReturn: toolResultVal };
			}
			if (userMessageOpts.from === MessageFrom.Plugin) {
				this._pluginTaskService.fireToolCall((userMessageOpts as PluginMessageOpts).taskInfo.taskId, toolName, toolCall.params);
				return { status: ToolCallStatus.success, name: toolName, error: '', content, toolCallReturn: null };
			}
		}

		try {
			if (!this.isNeedApprove(toolName)) {
				chatThreadService.updateToolCall(containerId, threadId, {
					toolCallResult: {
						status: ToolCallStatus.executing,
					} as ToolCallResultType,
				});
			}
			if (ToolNameEnum.EDIT_FILE === toolName) {
				const executeApply = userMessageOpts.chatMode === ChatMode.Ask ? false : true;
				// Only assign executeApply if params is an object with 'path' and 'content' properties (EDIT_FILE type)
				if (
					toolCall.params &&
					typeof toolCall.params === 'object' &&
					'path' in toolCall.params &&
					'code_block' in toolCall.params
				) {
					(toolCall.params as { path: string; code_block: string; executeApply?: boolean }).executeApply = executeApply;
				}
			}

			toolResultVal = await ideTool(toolCall.params as any, additionalOpt, onWait);
			content = this.toolResultToString[toolName](toolResultVal as any);
			return { status: ToolCallStatus.success, name: toolName, error: '', content, toolCallReturn: toolResultVal };
		} catch (error) {
			content = `Error calling tool: ${error.message}`
			return { status: ToolCallStatus.failure, name: toolName, error: error.message, content, toolCallReturn: null };
		} finally {
			this.threadId2CancelTokens.delete(threadId);
			cancellationSource.cancel();
			cancellationSource.dispose();
		}
	}

	cancelExecTool(threadId: string): void {
		const cancelToken = this.threadId2CancelTokens.get(threadId);
		if (cancelToken) {
			cancelToken.cancel();
			cancelToken.dispose();
		}
	}
}

registerSingleton(IToolsService, ToolsService, InstantiationType.Delayed);
